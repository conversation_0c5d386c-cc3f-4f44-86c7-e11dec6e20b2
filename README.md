# test_prachakit

A Flutter application for a mock online shopping app, developed as part of the SPW Flutter Mobile Challenge.

## Getting Started

Install dependencies
```bash
flutter pub get
```
Run the app
```bash
flutter run
```
Running test
```bash
cd test_prachakit
flutter test
flutter test --coverage
(Coverage report generated in coverage/ folder.)
```
Project Structure (MVVM)
```bash
lib/
├── models/ # Contains data models such as Product
├── repository/ # Handles data sources or mock data
├── viewModels/ # Manages business logic and application state
├── views/ # Contains UI screens, e.g., HomePage, CartPage
├── app.dart # Sets up app-level configuration (e.g., MaterialApp)
└── main.dart # Application entry point
```
Features
* Home screen with product listing from local JSON

* Favorite system with toggleable heart icon

* Cart screen with quantity, total price, and remove item functionality

* Product detail screen with image, price, and "Add to Cart" button

* Bottom navigation for Home, Saved, and Cart tabs

* MVVM architecture using Provider for state management

* Basic unit and widget tests included
