//Use to load data from json
import 'dart:convert';
import 'package:flutter/services.dart' show rootBundle;
import '../models/product.dart';

class ProductRepository {
  Future<List<Product>> fetchProducts() async {
    //loading data from json
    final jsonString = await rootBundle.loadString('assets/mock_data/product.json');
    final Map<String, dynamic> jsonMap = json.decode(jsonString); 
    final List<dynamic> jsonList = jsonMap['product_items']; 
    return jsonList.map((e) => Product.fromJson(e)).toList();
  }
}
