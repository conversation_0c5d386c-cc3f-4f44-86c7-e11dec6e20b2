  import 'package:flutter/foundation.dart';
  import '../models/cart_item.dart';

  class CartViewModel extends ChangeNotifier {

    //map by id
    final Map<int, CartItem> _items = {};

    //to list
    List<CartItem> get items => _items.values.toList();

    //quantity of item
    int get itemCount => _items.length;

    //get total price
    double get totalPrice => _items.values.fold(0, (sum, item) => sum + item.price * item.quantity);

    //add item to cart
    void addToCart(CartItem item) {
      if (_items.containsKey(item.productId)) {
        _items[item.productId]!.quantity += item.quantity;
      } else {
        _items[item.productId] = item;
      }
      notifyListeners();
    }

    //increase quantity item in cart
    void increaseQty(int productId) {
      if (_items.containsKey(productId)) {
        _items[productId]!.quantity += 1;
        notifyListeners();
      }
    }

    //decrease quantity item in cart
    void decreaseQty(int productId) {
      if (_items.containsKey(productId) && _items[productId]!.quantity > 1) {
        _items[productId]!.quantity -= 1;
        notifyListeners();
      }
      else if(_items.containsKey(productId) &&_items[productId]!.quantity==1){
        _items.remove(productId);
        notifyListeners();
      }
    }
  
    //remove item by id
    void removeFromCart(int productId) {
      _items.remove(productId);
      notifyListeners();
    }
  }
