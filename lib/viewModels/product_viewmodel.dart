import 'package:flutter/foundation.dart';
import '../models/product.dart';
import '../repository/product_repository.dart';


class ProductViewModel extends ChangeNotifier {
  final ProductRepository _repository;

  //Error message
  String? _errorMessage;
  String? get errorMessage => _errorMessage;

  ProductViewModel(this._repository){
    loadProducts();
  }
  //Store All Load Product
  List<Product> _products = [];

  //Store ID of Favorite Products
  final Set<int> _favoriteProductIDs = {};

  //Getter to access Products
  List<Product> get products => _products;

  //Getter to access Favorite Product
  List<Product> get favoriteProducts =>_products.where((product) => _favoriteProductIDs.contains(product.id)).toList();

  // Checks  product ID is favorite
  bool isFavorite(int id) => _favoriteProductIDs.contains(id);

  

  //Function to store and remove favorite product
  void toggleFavorite(int id){
    if(_favoriteProductIDs.contains(id)){
      _favoriteProductIDs.remove(id);
    }
    else{
      _favoriteProductIDs.add(id);
    }
    notifyListeners();
  }

  //Load data from repository
  Future<void> loadProducts() async {

    try {
      final loadedProducts = await _repository.fetchProducts();
      _products = loadedProducts;
      notifyListeners();
    } catch (e) {
      _errorMessage = "Connot load data";
    }
  }
}