import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'viewModels/product_viewmodel.dart';
import 'viewModels/navigation_viewmodel.dart';
import 'viewModels/cart_viewmodel.dart';
import 'app.dart';
import 'repository/product_repository.dart';

void main() {
  runApp( Main(productRepository: ProductRepository()));
}

class Main extends StatelessWidget {
  final ProductRepository productRepository;

  const Main({super.key, required this.productRepository});

  @override
  Widget build(BuildContext context) {
    return OrientationBuilder(
      builder: (context, orientation) {
        return ScreenUtilInit(
          designSize: orientation == Orientation.portrait
              ? const Size(375, 812)
              : const Size(812, 375),
          minTextAdapt: true,
          splitScreenMode: true,
          builder: (context, child) {
            return MultiProvider(
              providers: [
                //provider
                ChangeNotifierProvider(create: (_) => ProductViewModel(productRepository)),
                ChangeNotifierProvider(create: (_) => NavigationViewModel()),
                ChangeNotifierProvider(create: (_) => CartViewModel()),
              ],
              child: MaterialApp(
                title: 'Shop',
                theme: ThemeData(useMaterial3: true),
                home: const App(),
              ),
            );  
          },
        );
      },
    );
  }
}

