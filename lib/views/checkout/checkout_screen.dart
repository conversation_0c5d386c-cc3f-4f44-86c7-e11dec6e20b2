//checkout screen
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:qr_flutter/qr_flutter.dart';
import '../../models/cart_item.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../viewModels/cart_viewmodel.dart';
import '../widgets/common_appbar.dart';

class CheckoutScreen extends StatelessWidget {
    final List<CartItem> cartItems;
  const CheckoutScreen({super.key,required this.cartItems});

  @override
  Widget build(BuildContext context) {
    final cart = context.read<CartViewModel>();
    final total = cart.totalPrice.toStringAsFixed(2);
    final checkoutUrl = 'https://payment.spw.challenge/checkout?price=$total';

    final isPortrait = MediaQuery.of(context).orientation == Orientation.portrait;

    final qrSize = isPortrait ? 250.0.w : 180.0.w;
    final scanPayFontSize = isPortrait ? 30.sp : 20.sp;
    final priceFontSize = isPortrait ? 20.sp : 16.sp;
    final verticalSpacing = isPortrait ? 50.h : 20.h;

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: const CommonAppBar(
        title: "Checkout",
        showBack: true,
        titleStyle: TextStyle(fontWeight: FontWeight.bold),
      ),
      body:
      Center(
      child:SingleChildScrollView(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
            padding: EdgeInsets.all(20.w),
            decoration: BoxDecoration(
              border: Border.all(
                color: Colors.black,
                width: 10.w
              ),
            borderRadius: BorderRadius.circular(12.r)
            ),
            //make qr code from url
            child:QrImageView(
              key: const Key('qr_image'),
              data: checkoutUrl,
              version: QrVersions.auto,
              size: qrSize,
              backgroundColor: Colors.white,
            ),
            ),
            SizedBox(height: verticalSpacing),
            //scan & pay text
            Text(
              'SCAN & PAY',
              style: TextStyle(
                fontSize: scanPayFontSize,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: verticalSpacing),
            //total price
            Text('\$${cart.totalPrice.toStringAsFixed(2)}',
            key: const Key('checkout_total_price'),
            style: TextStyle(
                fontSize: priceFontSize,
                fontWeight: FontWeight.w600,           
            ),
            )
          ],
        ),
      ),
      ),
    );
  }
}