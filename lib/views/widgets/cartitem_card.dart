//cart
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import '../../models/cart_item.dart';

class CartItemCard extends StatelessWidget {
  final CartItem items;
  //callback to cart_viewmodel
  final VoidCallback onIncrease;
  final VoidCallback onDecrease;
  final VoidCallback onRemove;

  const CartItemCard({
    super.key,
    required this.items,
    required this.onIncrease,
    required this.onDecrease,
    required this.onRemove,
  });

  @override
  Widget build(BuildContext context) {
    final fontSize = 16.sp;
    final isPortrait = MediaQuery.of(context).orientation == Orientation.portrait;

    return SizedBox(
    height: isPortrait ? 150.h : 80.h,
    //sliable function
    //slide to unhide remove button
    child:Slidable(
          key: ValueKey(items.productId),
          endActionPane: ActionPane(
            motion: const DrawerMotion(),
            extentRatio: 0.25,
            children: [
              SlidableAction(
                onPressed: (_) => onRemove(),
                backgroundColor: Colors.red,
                foregroundColor: Colors.black,
                key: Key('remove_button_${items.productId}'),
                icon: Icons.delete,
                flex: 1,
                spacing: 0,
                borderRadius: BorderRadius.zero,
                )
            ]
            ),
          //card
          child:Card(
            key: ValueKey('card-${items.productId}'),
            margin: EdgeInsets.only(bottom: 0.h),
            elevation: 0,
            shape: const RoundedRectangleBorder(borderRadius: BorderRadius.zero ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  //image of product
                  Padding(
                  padding: EdgeInsets.only(left: 8.w),
                  child:ClipRRect(
                    child: Image.network(
                      items.imageUrl,
                      width: 150.w,
                      height: double.infinity,
                      fit: BoxFit.cover,
                      errorBuilder: (_, __, ___) => const Icon(Icons.image_not_supported),
                    ),
                  ),
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: Padding(
                      padding: EdgeInsets.all(8.w), 
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        //product name
                        Flexible(
                        child: Text(
                            items.name,
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: fontSize,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        SizedBox(height: 1.h),
                        //product price
                        Flexible(
                        child:Text(
                          '\$${items.price.toStringAsFixed(2)}',
                          style: TextStyle(
                            color: Colors.grey[700],
                            fontSize: fontSize,
                          ),
                        ),
                        ),
                        SizedBox(height: 1.h),
                        Container(
                        width: 140.w,
                        height: 35.h,
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(6.r)
                        ),
                        child: IntrinsicHeight(
                        //decrease product quantity
                        child: Row(
                          children: [
                            Expanded(
                              child: Center(
                                child: IconButton(
                                  key: Key('decrease_button_${items.productId}'),
                                  icon: const Icon(Icons.remove),
                                  onPressed: onDecrease,
                                  iconSize: 15.sp,
                                  splashColor: Colors.transparent,
                                  highlightColor: Colors.transparent,
                                ),
                              ),
                            ),
                            Container(
                              height: double.infinity,
                              width: 1,
                              color: Colors.grey,
                            ),
                            //product quantity
                            Expanded(
                              child: Center(
                                child: Text(
                                  '${items.quantity}',
                                  style: TextStyle(fontSize: fontSize),
                                ),
                              ),
                            ),
                            Container(
                              height: double.infinity,
                              width: 1,
                              color: Colors.grey,
                            ),
                            //increase product quantitsy
                            Expanded(
                              child: Center(
                                child: IconButton(
                                  key: Key('increase_button_${items.productId}'),
                                  icon: const Icon(Icons.add),
                                  onPressed: onIncrease,
                                  iconSize: 15.sp,
                                  splashColor: Colors.transparent,
                                  highlightColor: Colors.transparent,
                                ),
                              ),
                            ),
                          ],
                        ),
                        ),
                      )
                      ],
                    ),
                  )
                  ),
                ],
              ),
            ),
    )
    );
  }
}
