//menu
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomBottomNavigationBar extends StatelessWidget {
  final int selectedIndex;
  final Function(int) onItemTapped;

  const CustomBottomNavigationBar({
    super.key,
    required this.selectedIndex,
    required this.onItemTapped,
  });

  @override
  Widget build(BuildContext context) {
    final isPortrait = MediaQuery.of(context).orientation == Orientation.portrait;
    final double iconSize = isPortrait ? 30.w : 15.w;

    return Theme(
      data: ThemeData(
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
      ),
      child: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        items: [
          //home screen
          _buildNavItem(
            iconData: Icons.home,
            label: 'Home',
            isSelected: selectedIndex == 0,
            iconSize : iconSize
          ),
          //favorite screen
          _buildNavItem(
            iconData: Icons.favorite,
            label: 'Saved',
            isSelected: selectedIndex == 1,
            iconSize: iconSize
          ),
          //cart screen
          _buildNavItem(
            iconData: Icons.shopping_cart,
            label: 'Cart',
            isSelected: selectedIndex == 2,
            iconSize: iconSize
          ),
        ],
        currentIndex: selectedIndex.clamp(0, 2),
        selectedItemColor: Colors.grey,
        unselectedItemColor: Colors.grey,
        onTap: onItemTapped,
      ),
    );
  }
//menu bar
  BottomNavigationBarItem _buildNavItem({
    required IconData iconData,
    required String label,
    required bool isSelected,
    required double iconSize,
  }) {
    return BottomNavigationBarItem(
      icon: Icon(
        iconData,
        size: iconSize,
      ),
      label: label,
    );
  }
}
