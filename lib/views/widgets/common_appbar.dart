//appbar
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CommonAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String? title;
  //showBack to show back botton
  final bool showBack;
  final TextStyle? titleStyle;

  const CommonAppBar({
    super.key,
    this.title,
    this.showBack = false,
    this.titleStyle
  });

  @override
  Widget build(BuildContext context) {
  final isPortrait = MediaQuery.of(context).orientation == Orientation.portrait;
  final double paddingSize = isPortrait ? 8.w : 4.w;
  final double borderWidth = isPortrait ? 1.5.w : 0.75.w;
  final double iconSize = 24.w;

  return AppBar(
      //title
      title: title != null
      ? Text(
          title!,
          style: titleStyle
            )
      : null,
    surfaceTintColor: Colors.transparent,
    backgroundColor: Colors.white,
    automaticallyImplyLeading: false,
    leading: showBack
    //back bottn
        ? Padding(
            padding: EdgeInsets.all(paddingSize),
            child: GestureDetector(
              onTap: () {
                Navigator.of(context).pop();
              },
              child: Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.white,
                  border: Border.all(
                    color: Colors.black,
                    width: borderWidth, 
                  ),
                ),
                child: Icon(
                  Icons.chevron_left, 
                  color: Colors.black,
                  size: iconSize
                ),
              ),
            ),
          )
        : null,
  );
  }

//Send Heigth size of appbar
  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
