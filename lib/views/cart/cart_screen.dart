import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import '../checkout/checkout_screen.dart';
import '../../viewModels/cart_viewmodel.dart';
import '../widgets/common_appbar.dart';
import '../widgets/cartItem_card.dart';

class CartScreen extends StatelessWidget {
  const CartScreen({super.key});

  @override
  Widget build(BuildContext context) {

    //Get and listen to the cart data using Provider.
    final cart = context.watch<CartViewModel>();

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: const CommonAppBar(title: "Cart",  titleStyle: TextStyle(fontWeight: FontWeight.bold),),
      body: cart.items.isEmpty
          ? const Center(child: Text("Cart is empty"))
          : ListView.separated(
              itemCount: cart.items.length,
              itemBuilder: (_, index) {
              final item = cart.items[index];
              //Use cart item card from widget/cartiem_card.dart
              return CartItemCard(
                items : item, 
                onIncrease: () => cart.increaseQty(item.productId),
                onDecrease: () => cart.decreaseQty(item.productId),
                onRemove: () => cart.removeFromCart(item.productId),
                );
              },
              separatorBuilder: (_, __) => SizedBox(height: 15.h),
            ),

            //Total price and checkout button
            bottomNavigationBar: cart.items.isEmpty
            ? null
            : SafeArea(
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
                decoration: const BoxDecoration(
                  color: Colors.white,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                    child:Text(
                      'Total : \$${cart.totalPrice.toStringAsFixed(2)}',
                      style: TextStyle(
                        fontSize: 20.sp,
                        fontWeight: FontWeight.bold,
                        color: Colors.black
                      ),
                    ),
                    ),
                    SizedBox(width: 10.w),
                    ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.white,
                        padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(6.r),
                        ),
                        side: const BorderSide(
                          color: Colors.black,
                        )
                      ),
                      onPressed: (){
                        Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => ChangeNotifierProvider.value(
                                value: cart,
                                child: CheckoutScreen(cartItems: cart.items),
                              ),
                            ),
                        );
                      }, 
                  child: Text(
                      'Checkout',
                      style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.black
                  ),
                )
              )
            ],
          ),
        )
      )
    );
  }
}
