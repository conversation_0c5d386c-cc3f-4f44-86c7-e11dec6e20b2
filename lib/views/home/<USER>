//first page
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../viewModels/product_viewmodel.dart';
import '../widgets/product_card.dart';
import '../widgets/common_appbar.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});


  @override
  Widget build(BuildContext context) {

    //Listen ProductViewModel for changes
    final pvm = Provider.of<ProductViewModel>(context, listen: true);

    //Get all product
    final products = pvm.products;

    //Error Message
    final errorMessage = pvm.errorMessage;
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: const CommonAppBar(title: "For You", titleStyle: TextStyle(fontWeight: FontWeight.bold)),
      body: errorMessage != null
      ? Center(child: Text(errorMessage),)
      :products.isEmpty
      ? const Center(child: Text("Product is empty"),)
      : LayoutBuilder(
        builder: (context,constraints){
          return Padding(
            padding: EdgeInsets.all(12.w),
            child: GridView.builder(
              itemCount: products.length,
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      mainAxisSpacing: 12.w,
                      crossAxisSpacing: 12.w,
                      childAspectRatio: 3 / 4,
              ),
              itemBuilder: (context,index){
                final product = products[index];
                final isFavorite = pvm.isFavorite(product.id);
                //use productcard from ./widget/product_card
                return ProductCard(
                  product: product, 
                  isFavorite: isFavorite, 
                  onToggleFavorite:(){
                    //use toggle for store or remove favorite product
                    pvm.toggleFavorite(product.id);
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          isFavorite
                          ? 'Removed from favorite Products'
                          : 'Saved to favorite Products',
                        ),
                        duration: const Duration(milliseconds: 100),
                      )
                    );
                  }
                  );
              }
              ),
            );
        },
      )
    );
  }
}