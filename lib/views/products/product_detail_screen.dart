//Product detail page
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:test_prachakit/models/product.dart';
import 'package:test_prachakit/viewModels/product_viewmodel.dart';
import '../widgets/common_appbar.dart';
import '../../viewModels/cart_viewmodel.dart';
import '../../models/cart_item.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ProductDetailScreen extends StatelessWidget {
  final Product product;
  const ProductDetailScreen({super.key,required this.product});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CommonAppBar(showBack: true,),
      backgroundColor: Colors.white,
      body: LayoutBuilder(
        builder: (context, constraints){

          final pvm = Provider.of<ProductViewModel>(context);
          final isFavorite = pvm.isFavorite(product.id);

          final isPortrait = MediaQuery.of(context).orientation == Orientation.portrait;
          //image size
          final double imageHeight = isPortrait ? 0.5.sh : 0.8.sh;
          final double imageWidth = isPortrait ? 0.9.sw : 0.5.sw;
          //padding left and right
          final double sideMargin = isPortrait ? 0.07.sw : 0.6.sh;

          final double iconSize = isPortrait ? 0.08.sw : 0.06.sh;
          //padding right icon
          final double iconPadding = isPortrait ? 0.05.sw : 0.05.sh;
          //size of product name
          final double fontSizeTitle = isPortrait ? 0.07.sw : 0.05.sh;
          //size of product price
          final double fontSizePrice = isPortrait ? 0.07.sw : 0.05.sh;
          //space use in sizebox
          final double verticalSpacing = isPortrait ? 0.05.sh : 0.03.sw;

          final double borderRadius = isPortrait ? 12.r : 8.r;

          return pvm.products.isEmpty
          ? const  Center(child: Text("Product not found"),)
          : SingleChildScrollView(
          child:Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              //product image
              Align(
              alignment: Alignment.center,
              child:SizedBox(
                height: imageHeight,
                width: imageWidth,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(borderRadius),
                  child: 
                  Image.network(
                    product.imageUrl,
                    fit: BoxFit.cover,
                    errorBuilder: (_, __, ___) => const Placeholder(),
                  ),
                ),
              )
            ),
            SizedBox(height: verticalSpacing),
            Padding(
            padding: EdgeInsets.symmetric(horizontal: sideMargin),
            child : Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                  //product name
                    Expanded(
                      child:Text(
                        product.name,
                        style: TextStyle(
                          fontSize: fontSizeTitle,
                          fontWeight: FontWeight.bold
                        ),
                      ) 
                    ),
                    //favorite icon
                      Padding(
                        padding: EdgeInsets.only(right: iconPadding),
                        child: SizedBox(
                          width: iconSize + 8,
                          height: iconSize + 8,
                        child: IconButton
                        (
                        iconSize: iconSize,
                        onPressed: (){
                          pvm.toggleFavorite(product.id);
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content:Text(
                                isFavorite
                                  ? 'Remove from favorite'
                                  : 'Added to favorite'
                              ),
                            duration: const Duration(milliseconds: 100),
                            )
                          );
                        },
                        splashColor: Colors.transparent,
                        highlightColor: Colors.transparent,
                        icon: Icon(
                          isFavorite ? Icons.favorite :   Icons.favorite_border,
                          color: isFavorite ? Colors.red : Colors.grey,
                        ),
                      )
                    )
                    )
                  ],
                ),
                SizedBox(height: verticalSpacing),
                //product price
                Text(
                  '\$${product.price.toStringAsFixed(2)}',
                style: TextStyle(
                  fontSize: fontSizePrice,
                  fontWeight: FontWeight.bold,
                ),
                ),
                SizedBox(height: verticalSpacing),
                //add to cart botton
                Align(
                alignment: Alignment.center,
                child:
                SizedBox(
                width: isPortrait ? 0.9.sw : 0.9.sw,
                child:OutlinedButton(
                onPressed: () {
                    final cartVM = context.read<CartViewModel>();

                    cartVM.addToCart(CartItem(
                      productId: product.id,
                      name: product.name,
                      imageUrl: product.imageUrl,
                      price: product.price,
                      quantity: 1,
                    ));

                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Added to cart!'),
                      duration: Duration(milliseconds: 100),
                      ),
                      
                    );
                },
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  foregroundColor: Colors.black,
                  side: const BorderSide(color: Colors.black),
                  shape : RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(borderRadius)
                  )
                ),
                child: const Text("Add to Cart")
                )
                )
                )
              ],
            )
            ),
            ],
          )
          );
        }
      )
    );
  }
}