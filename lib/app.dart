// lib/app.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'viewModels/navigation_viewmodel.dart';
import 'views/home/<USER>';
import 'views/favorite/favorite_screen.dart';
import 'views/cart/cart_screen.dart';
import './views/widgets/custom_buttom_navbar.dart';

class App extends StatelessWidget {
  const App({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      theme: ThemeData(useMaterial3: true),
      home: const MainScaffold(),
    );
  }
}

class MainScaffold extends StatelessWidget {
  const MainScaffold({super.key});
  //list of screen
  final List<Widget> _pages = const [
    HomeScreen(),
    FavoriteScreen(),
    CartScreen()
  ];

  @override
  Widget build(BuildContext context) {
    final navigationVM = context.watch<NavigationViewModel>();
    //use menu from custom_bottom_navbar.dart
    return Scaffold(
      body: _pages[navigationVM.selectedIndex],
      bottomNavigationBar: CustomBottomNavigationBar(
        selectedIndex: navigationVM.selectedIndex,
        onItemTapped: (index) => context.read<NavigationViewModel>().selectIndex(index),
      ),
    );
  }
}
