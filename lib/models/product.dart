//model for product
//Reference : mock data assets/mock_data/product.json
class Product {
  final int id;
  final String name;
  final String imageUrl;
  final double price;


  Product({
    required this.id,
    required this.name,
    required this.imageUrl,
    required this.price
  });

  //convert json map to Product object
  factory Product.fromJson(Map<String, dynamic> json){
    return Product(
      id: json['id'], 
      name: json['name'],
      imageUrl: json['image_url'],
      price: (json['price'] as num).toDouble(), 
      );
  }
}