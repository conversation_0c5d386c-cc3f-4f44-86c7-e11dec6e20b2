import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:test_prachakit/app.dart';
import 'package:test_prachakit/repository/product_repository.dart';
import 'package:test_prachakit/viewModels/cart_viewmodel.dart';
import 'package:test_prachakit/viewModels/navigation_viewmodel.dart';
import 'package:test_prachakit/viewModels/product_viewmodel.dart';
import 'package:test_prachakit/views/home/<USER>';
import 'package:test_prachakit/views/favorite/favorite_screen.dart';
import 'package:test_prachakit/views/cart/cart_screen.dart';
import 'package:test_prachakit/views/widgets/custom_buttom_navbar.dart';

class TestNavigationViewModel extends NavigationViewModel {
  @override
  int selectedIndex = 0;
  
  @override
  void selectIndex(int index) {
    selectedIndex = index;
    notifyListeners();
  }
}

void main() {
//encapsulate by screenUtilInit
Widget createMainScaffold({required NavigationViewModel navVM}) {
  return ScreenUtilInit(
    designSize: const Size(360, 690),
    builder: (context, child) => MultiProvider(
      providers: [
        //provider
        ChangeNotifierProvider<NavigationViewModel>.value(value: navVM),
        ChangeNotifierProvider<ProductViewModel>(create: (_) => ProductViewModel(ProductRepository()),),
        ChangeNotifierProvider<CartViewModel>(create: (_) => CartViewModel(),),
      ],
      child: MaterialApp(
        home: child,
      ),
    ),
    child: const MainScaffold(),
  );
}

  testWidgets('MainScaffold shows HomeScreen by default', (tester) async {
    final navVM = TestNavigationViewModel();
    navVM.selectedIndex = 0;

    //render widget
    await tester.pumpWidget(createMainScaffold(navVM: navVM));
    await tester.pumpAndSettle();

    expect(find.byType(HomeScreen), findsOneWidget);
    expect(find.byType(FavoriteScreen), findsNothing);
    expect(find.byType(CartScreen), findsNothing);

    //check it's index 0 in menu
    final navbar = tester.widget<CustomBottomNavigationBar>(find.byType(CustomBottomNavigationBar));
    expect(navbar.selectedIndex, 0);
  });

  testWidgets('MainScaffold shows FavoriteScreen when selectedIndex=1', (tester) async {
    final navVM = TestNavigationViewModel();
    navVM.selectedIndex = 1;

   //render widget
    await tester.pumpWidget(createMainScaffold(navVM: navVM));
    await tester.pumpAndSettle();

    expect(find.byType(HomeScreen), findsNothing);
    expect(find.byType(FavoriteScreen), findsOneWidget);
    expect(find.byType(CartScreen), findsNothing);

    //check it's index 1 in menu
    final navbar = tester.widget<CustomBottomNavigationBar>(find.byType(CustomBottomNavigationBar));
    expect(navbar.selectedIndex, 1);
  });

  testWidgets('MainScaffold shows CartScreen when selectedIndex=2', (tester) async {
    final navVM = TestNavigationViewModel();
    navVM.selectedIndex = 2;

   //render widget
    await tester.pumpWidget(createMainScaffold(navVM: navVM));
    await tester.pumpAndSettle();

    expect(find.byType(HomeScreen), findsNothing);
    expect(find.byType(FavoriteScreen), findsNothing);
    expect(find.byType(CartScreen), findsOneWidget);

   //check it's index 2 in menu
    final navbar = tester.widget<CustomBottomNavigationBar>(find.byType(CustomBottomNavigationBar));
    expect(navbar.selectedIndex, 2);
  });

  testWidgets('Tapping CustomBottomNavigationBar calls selectIndex and updates screen', (tester) async {
    final navVM = TestNavigationViewModel();

    //render widget
    await tester.pumpWidget(createMainScaffold(navVM: navVM));
    await tester.pumpAndSettle();

    //check if it's homepage
    expect(navVM.selectedIndex, 0);
    expect(find.byType(HomeScreen), findsOneWidget);

    final navbarFinder = find.byType(CustomBottomNavigationBar);
    final navbar = tester.widget<CustomBottomNavigationBar>(navbarFinder);

    navbar.onItemTapped(2);
    await tester.pumpAndSettle();

    expect(navVM.selectedIndex, 2);
    expect(find.byType(CartScreen), findsOneWidget);
  });
}
