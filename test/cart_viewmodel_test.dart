import 'package:flutter_test/flutter_test.dart';
import 'package:test_prachakit/models/cart_item.dart';
import 'package:test_prachakit/viewmodels/cart_viewmodel.dart';

void main() {
  group('CartViewModel', () {
    late CartViewModel cart;

    setUp(() {
      cart = CartViewModel();
    });

    test('initial cart should be empty', () {
      expect(cart.items.length, 0);
      expect(cart.itemCount, 0);
      expect(cart.totalPrice, 0);
    });

    test('addToCart should add a new item', () {
      final item = CartItem(productId: 1, name: 'Item 1', price: 10.0, quantity: 1, imageUrl: '');
      cart.addToCart(item);
      expect(cart.items.length, 1);
      expect(cart.items[0].productId, 1);
      expect(cart.totalPrice, 10.0);
    });

    test('addToCart should increase quantity if item exists', () {
      final item = CartItem(productId: 1, name: 'Item 1', price: 10.0, quantity: 1, imageUrl: '');
      cart.addToCart(item);
      cart.addToCart(item);
      expect(cart.items[0].quantity, 2);
      expect(cart.totalPrice, 20.0);
    });

    test('increaseQty should increase item quantity by 1', () {
      final item = CartItem(productId: 1, name: 'Item 1', price: 5.0, quantity: 1, imageUrl: '');
      cart.addToCart(item);
      cart.increaseQty(1);
      expect(cart.items[0].quantity, 2);
      expect(cart.totalPrice, 10.0);
    });

    test('decreaseQty should decrease quantity or remove item if 1 left', () {
      final item = CartItem(productId: 1, name: 'Item 1', price: 5.0, quantity: 2, imageUrl: '');
      cart.addToCart(item);
      cart.decreaseQty(1);
      expect(cart.items[0].quantity, 1);
      cart.decreaseQty(1);
      expect(cart.items.length, 0); 
    });

    test('removeFromCart should remove item', () {
      final item = CartItem(productId: 1, name: 'Item 1', price: 5.0, quantity: 1, imageUrl: '');
      cart.addToCart(item);
      cart.removeFromCart(1);
      expect(cart.items.length, 0);
    });
  });
}
