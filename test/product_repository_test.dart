import 'package:flutter_test/flutter_test.dart';
import 'package:test_prachakit/models/product.dart';
import 'package:test_prachakit/repository/product_repository.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  test('fetchProducts returns list of products from JSON', () async {
    final repo = ProductRepository();

    final products = await repo.fetchProducts();

    expect(products, isA<List<Product>>());
    expect(products.length, 7);
    expect(products[0].name, '<PERSON><PERSON> White (New)');
    expect(products[1].price, 1200.0);
  });
}