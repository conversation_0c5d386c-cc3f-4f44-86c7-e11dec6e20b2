import 'package:flutter_test/flutter_test.dart';
import 'package:test_prachakit/models/product.dart';

void main() {
  group('Product', () {
    test('Product can be created with required fields', () {
      final product = Product(
        id: 1,
        name: 'Test Product',
        imageUrl: 'http://example.com/test.jpg',
        price: 99.99,
      );

      expect(product.id, 1);
      expect(product.name, 'Test Product');
      expect(product.imageUrl, 'http://example.com/test.jpg');
      expect(product.price, 99.99);
    });

    test('Product.fromJson converts JSON map to Product object correctly', () {
      final Map<String, dynamic> jsonMap = {
        'id': 5,
        'name': 'Sample Shoe',
        'image_url': 'http://example.com/shoe.png',
        'price': 1500.0,
      };

      final product = Product.fromJson(jsonMap);

      expect(product.id, 5);
      expect(product.name, 'Sample Shoe');
      expect(product.imageUrl, 'http://example.com/shoe.png');
      expect(product.price, 1500.0);
    });

    test('Product.fromJson handles integer price converted to double', () {
      final Map<String, dynamic> jsonMap = {
        'id': 6,
        'name': 'Simple Shirt',
        'image_url': 'http://example.com/shirt.png',
        'price': 500,
      };

      final product = Product.fromJson(jsonMap);

      expect(product.id, 6);
      expect(product.name, 'Simple Shirt');
      expect(product.imageUrl, 'http://example.com/shirt.png');
      expect(product.price, 500.0);
      expect(product.price, isA<double>());
    });

    test('Product.fromJson throws error if required fields are missing', () {
      final Map<String, dynamic> jsonMap = {
        'id': 7,
        'name': 'Product Without Image',
        'price': 75.0,
      };

      expect(() => Product.fromJson(jsonMap), throwsA(isA<TypeError>()));
    });
  });
}