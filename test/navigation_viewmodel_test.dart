import 'package:flutter_test/flutter_test.dart';
import 'package:test_prachakit/viewmodels/navigation_viewmodel.dart';

void main() {
  group('NavigationViewModel', () {
    late NavigationViewModel nav;

    setUp(() {
      nav = NavigationViewModel();
    });

    test('initial selectedIndex should be 0', () {
      expect(nav.selectedIndex, 0);
    });

    test('selectIndex should change index and notify listeners', () {
      bool notified = false;
      nav.addListener(() {
        notified = true;
      });

      nav.selectIndex(2);

      expect(nav.selectedIndex, 2);
      expect(notified, true);
    });

    test('selectIndex should not notify if index is the same', () {
      bool notified = false;
      nav.addListener(() {
        notified = true;
      });

      nav.selectIndex(0); 

      expect(nav.selectedIndex, 0);
      expect(notified, false);
    });
  });
}
