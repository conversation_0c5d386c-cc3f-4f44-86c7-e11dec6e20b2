import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:test_prachakit/models/product.dart';
import 'package:test_prachakit/viewModels/product_viewmodel.dart';
import 'package:test_prachakit/views/home/<USER>';


class TestProductViewModel extends ChangeNotifier implements ProductViewModel {
  @override
  List<Product> products = [];

  @override
  List<Product> favoriteProducts = [];

  @override
  String? errorMessage;

  final List<int> toggled = [];

  @override
  bool isFavorite(int id) => favoriteProducts.any((p) => p.id == id);

  @override
  void toggleFavorite(int id) {
    toggled.add(id);
    notifyListeners();
  }

  @override
  Future<void> loadProducts() {
    throw UnimplementedError();
  }
}

Widget createWidgetUnderTest(ProductViewModel viewModel) {
  return ScreenUtilInit(
    designSize: const Size(360, 690),
    builder: (_, __) => MaterialApp(
      home: ChangeNotifierProvider<ProductViewModel>.value(
        value: viewModel,
        child: const HomeScreen(),
      ),
    ),
  );
}

void main() {
  testWidgets('shows empty message when no products', (tester) async {
    final viewModel = TestProductViewModel();
    viewModel.products = [];

    await tester.pumpWidget(createWidgetUnderTest(viewModel));
    await tester.pumpAndSettle();

    expect(find.text('Product is empty'), findsOneWidget);
  });

  testWidgets('shows error message when errorMessage is not null', (tester) async {
    final viewModel = TestProductViewModel();
    viewModel.errorMessage = 'Network error';

    await tester.pumpWidget(createWidgetUnderTest(viewModel));
    await tester.pumpAndSettle();

    expect(find.text('Network error'), findsOneWidget);
  });

  testWidgets('shows products in grid', (tester) async {
    final viewModel = TestProductViewModel();
    viewModel.products = [
      Product(id: 1, name: 'Product 1', imageUrl: '', price: 100),
      Product(id: 2, name: 'Product 2', imageUrl: '', price: 200),
    ];
    viewModel.favoriteProducts = [viewModel.products[0]]; // product 1 is favorite

    await tester.pumpWidget(createWidgetUnderTest(viewModel));
    await tester.pumpAndSettle();

    expect(find.text('Product 1'), findsOneWidget);
    expect(find.text('Product 2'), findsOneWidget);
    expect(find.byType(GridView), findsOneWidget);
  });

  testWidgets('tapping favorite icon toggles favorite', (tester) async {
    final viewModel = TestProductViewModel();
    final product = Product(id: 1, name: 'Product 1', imageUrl: '', price: 100);
    viewModel.products = [product];
    viewModel.favoriteProducts = [product];

    await tester.pumpWidget(createWidgetUnderTest(viewModel));
    await tester.pumpAndSettle();

    final favIcon = find.byIcon(Icons.favorite);
    expect(favIcon, findsOneWidget);

    await tester.tap(favIcon);
    await tester.pump(); // เพื่อ trigger snackbar

    expect(viewModel.toggled.contains(product.id), isTrue);
    expect(find.text('Removed from favorite Products'), findsOneWidget);
  });
}
