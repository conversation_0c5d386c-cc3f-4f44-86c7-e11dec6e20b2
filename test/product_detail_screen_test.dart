  import 'package:flutter/material.dart';
  import 'package:flutter_test/flutter_test.dart';
  import 'package:provider/provider.dart';
  import 'package:flutter_screenutil/flutter_screenutil.dart';

  import 'package:test_prachakit/models/product.dart';
  import 'package:test_prachakit/viewModels/product_viewmodel.dart';
  import 'package:test_prachakit/viewModels/cart_viewmodel.dart';
  import 'package:test_prachakit/models/cart_item.dart';
  import 'package:test_prachakit/views/products/product_detail_screen.dart';

  class TestProductViewModel extends ChangeNotifier implements ProductViewModel {
    @override
    List<Product> products = [];

    @override
    List<Product> favoriteProducts = [];

    @override
    String? errorMessage;

    final List<int> toggled = [];

    @override
    bool isFavorite(int id) => favoriteProducts.any((p) => p.id == id);

    @override
    void toggleFavorite(int id) {
      toggled.add(id);
      // toggle logic for test (just add/remove for demo)
      if (favoriteProducts.any((p) => p.id == id)) {
        favoriteProducts.removeWhere((p) => p.id == id);
      } else {
        favoriteProducts.add(products.firstWhere((p) => p.id == id));
      }
      notifyListeners();
    }

    @override
    Future<void> loadProducts() {
      throw UnimplementedError();
    }
  }

  class TestCartViewModel extends ChangeNotifier implements CartViewModel {
    final List<CartItem> addedItems = [];

    @override
    void addToCart(CartItem item) {
      addedItems.add(item);
      notifyListeners();
    }

    @override
    void removeFromCart(int productId) {
    }

    List<CartItem> get cartItems => addedItems;

    @override
    void increaseQty(int productId) {}

    @override
    void decreaseQty(int productId) {}

    @override
    List<CartItem> get items => addedItems;

    @override
    int get itemCount => addedItems.length;

    @override
    double get totalPrice => addedItems.fold(0, (sum, item) => sum + item.price * item.quantity);
  }

  Widget createWidgetUnderTest({required Product product, required ProductViewModel pvm, required CartViewModel cartVM}) {
    return ScreenUtilInit(
      designSize: const Size(360, 690),
      builder: (_, __) => MultiProvider(
        providers: [
          ChangeNotifierProvider<ProductViewModel>.value(value: pvm),
          ChangeNotifierProvider<CartViewModel>.value(value: cartVM),
        ],
        child: MaterialApp(
        home: Scaffold(
          body: ProductDetailScreen(product: product),
        ),
        ),
      ),
    );
  }

  void main() {
    final testProduct = Product(id: 1, name: 'Test Product', imageUrl: 'https://example.com/image.png', price: 123.45);

    testWidgets('displays product details correctly', (tester) async {
      final pvm = TestProductViewModel();
      final cartVM = TestCartViewModel();

      pvm.products = [testProduct];
      pvm.favoriteProducts = []; 

      await tester.pumpWidget(createWidgetUnderTest(product: testProduct, pvm: pvm, cartVM: cartVM));
      await tester.pumpAndSettle();

      expect(find.text('Test Product'), findsOneWidget);
      expect(find.text('\$123.45'), findsOneWidget);
      expect(find.byType(Image), findsOneWidget);

      // favorite icon should be border (not favorite)
      expect(find.byIcon(Icons.favorite_border), findsOneWidget);
    });

    testWidgets('toggle favorite shows snackbar and updates icon', (tester) async {
      tester.view.physicalSize = const Size(1080, 1920);
      tester.view.devicePixelRatio = 1.0;
      final pvm = TestProductViewModel();
      final cartVM = TestCartViewModel();

      pvm.products = [testProduct];
      pvm.favoriteProducts = [];

      await tester.pumpWidget(createWidgetUnderTest(product: testProduct, pvm: pvm, cartVM: cartVM));
      await tester.pumpAndSettle();

      // Tap favorite icon (border)
      final favButton = find.byIcon(Icons.favorite_border);
      expect(favButton, findsOneWidget);

      //toggle favorite
      await tester.tap(favButton);
      await tester.pumpAndSettle();

      expect(pvm.isFavorite(testProduct.id), isTrue);

    });

    testWidgets('add to cart button adds product and shows snackbar', (tester) async {
      final pvm = TestProductViewModel();
      final cartVM = TestCartViewModel();

      pvm.products = [testProduct];
      pvm.favoriteProducts = [];

      await tester.pumpWidget(createWidgetUnderTest(product: testProduct, pvm: pvm, cartVM: cartVM));
      await tester.pumpAndSettle();

      final addToCartButton = find.text('Add to Cart');
      expect(addToCartButton, findsOneWidget);

      await tester.tap(addToCartButton);
      await tester.pumpAndSettle();


      // Check in cart
      expect(cartVM.addedItems.length, 1);
      expect(cartVM.addedItems.first.productId, testProduct.id);
    });
  }
