import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:test_prachakit/models/cart_item.dart';
import 'package:test_prachakit/viewModels/cart_viewmodel.dart';
import 'package:test_prachakit/views/checkout/checkout_screen.dart';
import 'package:network_image_mock/network_image_mock.dart';

void main() {
  Widget createCheckoutScreen({required CartViewModel cartViewModel}) {
    return ScreenUtilInit(
      designSize: const Size(360, 690),
      builder: (context, child) => ChangeNotifierProvider<CartViewModel>.value(
        value: cartViewModel,
        child: const MaterialApp(
          home: CheckoutScreen(cartItems: []),
        ),
      ),
    );
  }

  testWidgets('CheckoutScreen displays QR, SCAN & PAY and correct total price', (tester) async {
    final cartViewModel = CartViewModel();
    cartViewModel.addToCart(CartItem(
      productId: 1,
      name: 'Item 1',
      imageUrl: 'url1',
      price: 30.0,
      quantity: 2, 
    ));

    await mockNetworkImagesFor(() async {
      await tester.pumpWidget(createCheckoutScreen(cartViewModel: cartViewModel));
      await tester.pumpAndSettle();

      // Check for QR code
      expect(find.byKey(const Key('qr_image')), findsOneWidget);

      // Check for "SCAN & PAY" text
      expect(find.text('SCAN & PAY'), findsOneWidget);

      // Check for total price
      final priceTextFinder = find.byKey(const Key('checkout_total_price'));
      expect(priceTextFinder, findsOneWidget);
      expect(
        tester.widget<Text>(priceTextFinder).data,
        equals('\$60.00'),
      );
    });
  });
}
