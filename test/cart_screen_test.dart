import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:provider/provider.dart';
import 'package:test_prachakit/models/cart_item.dart';
import 'package:test_prachakit/viewModels/cart_viewmodel.dart';
import 'package:test_prachakit/views/cart/cart_screen.dart';
import 'package:network_image_mock/network_image_mock.dart';

//mock for navigator
class MockNavigatorObserver extends Mock implements NavigatorObserver {}
class FakeRoute extends Fake implements Route<dynamic> {}


void main() {
  late MockNavigatorObserver mockNavigatorObserver;

  setUpAll((){
    registerFallbackValue(FakeRoute());
  }
  );
  setUp(() {
    mockNavigatorObserver = MockNavigatorObserver();
  });
  tearDown(() {
    reset(mockNavigatorObserver);
  });
  Widget createCartScreen({required CartViewModel   cartViewModel}) {
    return ScreenUtilInit(
      designSize: const Size(360, 690),
      builder: (context, child) => MaterialApp(
        home: ChangeNotifierProvider<CartViewModel>.value(
          value: cartViewModel,
          child: const CartScreen(),
        ),
        navigatorObservers: [mockNavigatorObserver],
      ),
    );
  }
  group('CartScreen UI Tests', () {
    testWidgets('displays "Cart is empty" when cart is empty', (tester) async {
      final cartViewModel = CartViewModel();

      await mockNetworkImagesFor(() async {
      await tester.pumpWidget(createCartScreen(cartViewModel: cartViewModel));
      await tester.pumpAndSettle();

      expect(find.text('Cart'), findsOneWidget);
      expect(find.text('Cart is empty'), findsOneWidget); 
      expect(find.byType(ListView), findsNothing);
      expect(find.byType(ElevatedButton), findsNothing); 
      expect(find.textContaining('Total'), findsNothing); 
    });
  });
 testWidgets('displays cart items when cart is not empty', (tester) async {
      final cartViewModel = CartViewModel();
      final cartItem1 = CartItem(productId: 1, name: 'Item 1', imageUrl: 'url1', price: 10.0, quantity: 1);
      final cartItem2 = CartItem(productId: 2, name: 'Item 2', imageUrl: 'url2', price: 25.0, quantity: 2);
      cartViewModel.addToCart(cartItem1);
      cartViewModel.addToCart(cartItem2);
     await mockNetworkImagesFor(() async {
        await tester.pumpWidget(createCartScreen(cartViewModel: cartViewModel));
        await tester.pumpAndSettle();

        expect(find.text('Cart'), findsOneWidget);
        expect(find.text('Cart is empty'), findsNothing);

        expect(find.byType(ListView), findsOneWidget);
        expect(find.byKey(const ValueKey('card-1')), findsOneWidget);
        expect(find.byKey(const ValueKey('card-2')), findsOneWidget);
        expect(find.text('Total : \$60.00'), findsOneWidget);
        expect(find.text('Checkout'), findsOneWidget);

        expect(find.text('Item 1'), findsOneWidget);
        expect(find.text('\$10.00'), findsOneWidget);
        expect(find.text('1'), findsOneWidget);

        expect(find.text('Item 2'), findsOneWidget);
        expect(find.text('\$25.00'), findsOneWidget);
        expect(find.text('2'), findsOneWidget);
      });
  });
  testWidgets('tapping checkout navigates to CheckoutScreen', (tester) async {
    final cartViewModel = CartViewModel();
    cartViewModel.addToCart(CartItem(productId: 1, name: 'Item 1', imageUrl: 'url1', price: 10.0, quantity: 1));

    await mockNetworkImagesFor(() async {
      await tester.pumpWidget(createCartScreen(cartViewModel: cartViewModel));
      await tester.pumpAndSettle();

      //find check out button
      expect(find.text('Checkout'), findsOneWidget);
      await tester.tap(find.text('Checkout'));
      await tester.pumpAndSettle();

      verify(() => mockNavigatorObserver.didPush(any(), any())).called(2);
    });
  });
    testWidgets('increase, decrease and remove buttons call corresponding CartViewModel methods', (tester) async {
      final cartViewModel = CartViewModel();
      final cartItem = CartItem(productId: 1,name: 'Item 1',imageUrl: 'url1',price: 10.0,quantity: 1);
      cartViewModel.addToCart(cartItem);

      await mockNetworkImagesFor(() async {
        await tester.pumpWidget(createCartScreen(cartViewModel: cartViewModel));
        await tester.pumpAndSettle();

        // find increase button and tap
        final increaseButton = find.byKey(Key('increase_button_${cartItem.productId}'));
        expect(increaseButton, findsOneWidget);
        await tester.tap(increaseButton);
        await tester.pump();
        expect(cartViewModel.items.first.quantity, 2); 
    
        
        // find decrease button and tap
        final decreaseButton = find.byKey(Key('decrease_button_${cartItem.productId}'));
        expect(decreaseButton, findsOneWidget);
        await tester.tap(decreaseButton);
        await tester.pump();
        expect(cartViewModel.items.first.quantity, 1);

        // slide card
        final slidable = find.byKey(ValueKey(cartItem.productId));
        expect(slidable, findsOneWidget);
        await tester.drag(slidable, const Offset(-300, 0));
        await tester.pumpAndSettle();

        // find remove button and tap
        final removeButton = find.byKey(Key('remove_button_${cartItem.productId}'));
        expect(removeButton, findsOneWidget);
        await tester.tap(removeButton);
        await tester.pump();
      });
    });

  });
}