import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:test_prachakit/viewmodels/product_viewmodel.dart';
import 'package:test_prachakit/models/product.dart';
import 'package:test_prachakit/repository/product_repository.dart';

// create mock class for ProductRepository
class MockProductRepository extends Mock implements ProductRepository {}

void main() {
  late MockProductRepository mockRepository;
  late ProductViewModel viewModel;

  final sampleProducts = [
    Product(id: 1, name: 'Product 1', price: 10.0, imageUrl: ''),
    Product(id: 2, name: 'Product 2', price: 20.0, imageUrl: ''),
  ];

  setUp(() {
    mockRepository = MockProductRepository();
    when(() => mockRepository.fetchProducts()).thenAnswer((_) async => sampleProducts);

    viewModel = ProductViewModel(mockRepository);
  });


  test('loadProducts loads products and notifies listeners', () async {
    await viewModel.loadProducts();
    expect(viewModel.products, sampleProducts);
    expect(viewModel.errorMessage, isNull);
  });

  test('toggleFavorite adds and removes favorites', () {
    expect(viewModel.isFavorite(1), isFalse);

    viewModel.toggleFavorite(1);
    expect(viewModel.isFavorite(1), isTrue);

    viewModel.toggleFavorite(1);
    expect(viewModel.isFavorite(1), isFalse);
  });

  test('favoriteProducts returns only favorites', () {
    viewModel.toggleFavorite(1);
    viewModel.toggleFavorite(2);
    expect(viewModel.favoriteProducts.length, 2);

    viewModel.toggleFavorite(1);
    expect(viewModel.favoriteProducts.length, 1);
  });
}
