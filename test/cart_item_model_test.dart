import 'package:flutter_test/flutter_test.dart';
import 'package:test_prachakit/models/cart_item.dart';

void main() {
  group('CartItem', () {
    test('CartItem can be created with required fields and default quantity', () {
      final cartItem = CartItem(
        productId: 1,
        name: 'Product A',
        imageUrl: 'http://example.com/a.jpg',
        price: 100.0,
      );

      expect(cartItem.productId, 1);
      expect(cartItem.name, 'Product A');
      expect(cartItem.imageUrl, 'http://example.com/a.jpg');
      expect(cartItem.price, 100.0);
      expect(cartItem.quantity, 1);
    });

    test('CartItem can be created with a custom quantity', () {
      final cartItem = CartItem(
        productId: 2,
        name: 'Product B',
        imageUrl: 'http://example.com/b.jpg',
        price: 250.0,
        quantity: 5,
      );

      expect(cartItem.productId, 2);
      expect(cartItem.name, 'Product B');
      expect(cartItem.imageUrl, 'http://example.com/b.jpg');
      expect(cartItem.price, 250.0);
      expect(cartItem.quantity, 5);
    });

    test('quantity can be updated', () {
      final cartItem = CartItem(
        productId: 3,
        name: 'Product C',
        imageUrl: 'http://example.com/c.jpg',
        price: 50.0,
      );

      expect(cartItem.quantity, 1);

      cartItem.quantity = 2;
      expect(cartItem.quantity, 2);

      cartItem.quantity += 3;
      expect(cartItem.quantity, 5);
    });
  });
}